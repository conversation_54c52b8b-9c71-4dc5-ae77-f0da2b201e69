import React, { useCallback, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Pressable,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  closeBottomSheet,
  openBottomSheet,
} from 'molecules/BottomSheet/BottomSheet';
import dark from 'core/constants/themes/dark';
import _size from 'lodash/size';
import _isEmpty from 'lodash/isEmpty';
import _trim from 'lodash/trim';

import { closePopover, showPopover } from 'molecules/Popover/Popover';
import ActionPopoverContent from '@/src/components/shared/ActionPopoverContent';
import _toUpper from 'lodash/toUpper';
import _isEqual from 'lodash/isEqual';
import _isFunction from 'lodash/isFunction';
import useInviteFriendOnMatiks from 'core/hooks/useInviteFriendOnMatiks';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { router, useLocalSearchParams } from 'expo-router';
import DefaultCollegeIcon from 'atoms/DefaultCollegeIcon';
import Icon, { ICON_TYPES } from 'atoms/Icon';
import useGoBack from 'navigator/hooks/useGoBack';
import userReader from 'core/readers/userReader';
import _isNil from 'lodash/isNil';
import ExitCollegeBottomSheet from 'modules/profile/components/ExitCollegeBottomSheet';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import useUpdateUserProfile from 'modules/profile/hooks/query/useUpdateUserProfile';
import Analytics from 'core/analytics';
import AddCollegeFriendsScreen from '../../../friends/pages/AddCollegeFriendsScreen';
import useSearchInstitutions from '../../hooks/query/useSearchInstitutions';
import InstitutionListItem from '../InstitutionListItem';
import AddInstitutionForm from '../AddInstitutionForm';
import styles from './SelectInstitutionComponent.style';

const SelectInstitutionComponent = ({
  onInstitutionSelect,
}: {
  onInstitutionSelect: Function;
}) => {
  const [selectedForPopover, setSelectedForPopover] = useState<any>(null);
  const { user, updateCurrentUser } = useSession();
  const { updateUser } = useUpdateUserProfile();

  const currentUserInstitutionId = userReader.institutionId(user);
  const currentUserInstitutionName = userReader.institutionName(user);

  const { goBack } = useGoBack();

  const { instituteName }: { instituteName: string } = useLocalSearchParams();

  const {
    searchTerm,
    setSearchTerm,
    institutions,
    loading: searchLoading,
    error: searchError,
  } = useSearchInstitutions();

  const textInputRef = React.useRef<TextInput>(null);

  const handleOnTextChange = useCallback(
    (text: string) => {
      setSearchTerm(text);
      router.setParams({ instituteName: text });
    },
    [setSearchTerm],
  );

  useEffect(() => {
    textInputRef?.current?.focus();
    if (instituteName) {
      setSearchTerm(instituteName);
      setSelectedForPopover({
        name: instituteName,
        id: currentUserInstitutionId,
      });
    }
  }, []);

  const handleConfirmJoin = useCallback(
    (institution: any) => {
      if (_isEqual(currentUserInstitutionId, institution?.id)) return;
      if (_isFunction(onInstitutionSelect)) {
        onInstitutionSelect?.(institution).then(() => {
          setSelectedForPopover(null);
          showPopover({
            content: (
              <AddCollegeFriendsScreen
                isPopoverMode
                onCloseRequest={() => closePopover()}
              />
            ),
            overlayLook: true,
            style: {
              width: '100%',
              height: '100%',
              backgroundColor: dark.colors.background,
              borderRadius: 0,
              padding: 0,
              margin: 0,
              borderWidth: 0,
              maxWidth: 550,
            },
            backdropStyle: { backgroundColor: 'rgba(0,0,0,0.6)' },
          });
        });
      }
    },
    [currentUserInstitutionId, onInstitutionSelect],
  );

  const handleSelectAndShowJoinPopover = useCallback(
    (institution: any) => {
      setSelectedForPopover(institution);
      if (_isEqual(currentUserInstitutionId, institution?.id)) return;
      showPopover({
        content: (
          <ActionPopoverContent
            iconPlaceholder
            renderIconPlaceholder={() => (
              <View style={styles.popoverImagePlaceholder}>
                <DefaultCollegeIcon size={40} />
              </View>
            )}
            title={`JOIN ${_toUpper(_trim(institution?.name) || 'INSTITUTION')} ?`}
            message="This will be shown on your profile page."
            primaryActionText="OKAY, CONFIRM"
            onPrimaryAction={() => handleConfirmJoin(institution)}
            secondaryActionText="NO, I DONT WANT THIS"
            onSecondaryAction={() => {
              closePopover();
              setSelectedForPopover(null);
            }}
          />
        ),
        overlayLook: true,
        style: {
          padding: 0,
          margin: 0,
          borderRadius: 12,
          borderWidth: 0,
          backgroundColor: 'transparent',
        },
        backdropStyle: { backgroundColor: 'rgba(0,0,0,0.6)' },
      });
    },
    [currentUserInstitutionId, handleConfirmJoin],
  );

  const { handleNormalShare } = useInviteFriendOnMatiks();

  const handleOnInviteFriendsPress = useCallback(() => {
    closePopover();
    handleNormalShare({
      eventToBeTracked:
        ANALYTICS_EVENTS.PROFILE.INVITE_FRIENDS_BUTTON_AFTER_COLLEGE_CREATION,
    });
  }, [handleNormalShare]);

  const handleInstitutionCreated = useCallback(
    (newlyCreatedInstitution: any) => {
      closeBottomSheet();
      if (_isFunction(onInstitutionSelect)) {
        onInstitutionSelect?.(newlyCreatedInstitution);
      }
      showPopover({
        content: (
          <ActionPopoverContent
            iconPlaceholder
            renderIconPlaceholder={() => (
              <View style={styles.popoverImagePlaceholder}>
                <DefaultCollegeIcon size={40} />
              </View>
            )}
            title="INSTITUTION CREATED"
            titleColor={dark.colors.streak}
            message={`${_trim(newlyCreatedInstitution?.name) || 'Your college'} has been created. Invite your friends to join the group`}
            primaryActionText="INVITE FRIENDS"
            onPrimaryAction={handleOnInviteFriendsPress}
            secondaryActionText="DO IT LATER"
            onSecondaryAction={closePopover}
          />
        ),
        overlayLook: true,
        style: {
          padding: 0,
          margin: 0,
          borderRadius: 12,
          borderWidth: 0,
          backgroundColor: 'transparent',
        },
        backdropStyle: { backgroundColor: 'rgba(0,0,0,0.7)' },
      });
    },
    [handleOnInviteFriendsPress, onInstitutionSelect],
  );

  const handleChangeCollege = useCallback(() => {
    closeBottomSheet();
    setSearchTerm('');
    textInputRef.current?.focus();
    Analytics.track(ANALYTICS_EVENTS.PROFILE.CLICKED_ON_CHANGE_COLLEGE);
    router.setParams({ instituteName: '' });
  }, [setSearchTerm]);

  const handleRemoveCollege = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.PROFILE.CLICKED_ON_REMOVE_COLLEGE);
    try {
      updateCurrentUser?.({ institutionId: null, institutionName: null });
      closeBottomSheet?.();
      goBack?.();
      updateUser?.({ removeInstitution: true })
        .then(() => {
          showToast({
            type: TOAST_TYPE.SUCCESS,
            description: 'College removed successfully!',
          });
        })
        .then(() => {
          Analytics.track(ANALYTICS_EVENTS.PROFILE.REMOVED_COLLEGE);
        });
    } catch (error) {
      console.error('Failed to remove college:', error);
    }
  }, [updateUser, goBack, updateCurrentUser]);

  const handleOpenExitBottomSheet = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.PROFILE.CLICKED_ON_EXIT_COLLEGE_ICON);
    textInputRef.current?.blur();
    openBottomSheet({
      content: (
        <ExitCollegeBottomSheet
          institutionName={currentUserInstitutionName}
          onChangeCollege={handleChangeCollege}
          onRemoveCollege={handleRemoveCollege}
        />
      ),
      styles: {
        frame: {
          borderTopColor: dark.colors.defeatColor,
          padding: 0,
        },
      },
    });
  }, [handleChangeCollege, handleRemoveCollege, currentUserInstitutionName]);

  const handleAddNewClick = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.PROFILE.CLICKED_ON_ADD_NEW_COLLEGE);
    openBottomSheet({
      content: (
        <AddInstitutionForm
          onInstitutionCreated={handleInstitutionCreated}
          instituteName={instituteName}
        />
      ),
    });
  }, [handleInstitutionCreated, instituteName]);

  const renderItem = ({ item }: { item: any }) => (
    <InstitutionListItem
      institution={item}
      onPress={() => handleSelectAndShowJoinPopover(item)}
      isSelected={_isEqual(selectedForPopover?.id, item.id)}
    />
  );

  const renderListFooter = () => {
    if (_size(searchTerm) < 3) return null;
    if (_size(searchTerm) > 3 && !_isEmpty(institutions)) return null;

    return (
      <View style={styles.footerContainer}>
        <Text
          style={styles.emptyText}
          numberOfLines={1}
        >{`No results found for "${searchTerm}"`}</Text>
        <Pressable onPress={handleAddNewClick} style={styles.addNewButton}>
          <View style={styles.logoContainer}>
            <DefaultCollegeIcon size={20} />
          </View>
          <Text style={styles.addNewText} numberOfLines={1}>
            {`Add "${searchTerm}" as a school/college`}
          </Text>
        </Pressable>
      </View>
    );
  };

  return (
    <View style={{ flex: 1 }}>
      <View style={styles.container}>
        <View
          style={{
            flexDirection: 'row',
            gap: 8,
            alignItems: 'center',
            paddingVertical: 12,
          }}
        >
          <TouchableOpacity onPress={goBack}>
            <Icon
              name="arrow-back"
              type={ICON_TYPES.IONICON}
              color={dark.colors.textLight}
              size={20}
            />
          </TouchableOpacity>
          <View style={{ flex: 1 }}>
            <TextInput
              ref={textInputRef}
              style={styles.input}
              placeholder="Search for your College / School"
              placeholderTextColor={dark.colors.placeholder}
              value={searchTerm}
              onChangeText={handleOnTextChange}
              allowFontScaling={false}
            />
          </View>
          {!_isNil(currentUserInstitutionId) && (
            <TouchableOpacity onPress={handleOpenExitBottomSheet}>
              <Icon
                name="exit-outline"
                type={ICON_TYPES.IONICON}
                color={dark.colors.textLight}
                size={20}
              />
            </TouchableOpacity>
          )}
        </View>

        {searchLoading && (
          <ActivityIndicator
            style={styles.loader}
            size="small"
            color={dark.colors.secondary}
          />
        )}

        {searchError && (
          <Text style={styles.errorText}>
            Error searching: {searchError?.message}
          </Text>
        )}

        <FlatList
          style={styles.list}
          data={institutions}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          ListFooterComponent={renderListFooter}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        />
      </View>
    </View>
  );
};

export default React.memo(SelectInstitutionComponent);
