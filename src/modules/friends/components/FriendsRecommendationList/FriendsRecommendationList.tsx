import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from 'core/readers/userReader';
import { router } from 'expo-router';
import _map from 'lodash/map';
import FriendSuggestionCard from 'modules/friends/components/FriendSuggestionCard';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import useIsAddCollegeFeatureAvailable from '@/src/hooks/features/useIsAddCollegeFeatureAvailable';
import useGetUsersOfMyInstitute from '../../hooks/queries/useGetUsersOfMyInstitute';
import styles from './FriendsRecommendationList.style';

const FriendsRecommendationList = () => {
  const { user } = useSession();
  const isAddCollegeFeatureAvailable = useIsAddCollegeFeatureAvailable();
  const currentUserInstitutionId = userReader.institutionId(user);

  const navigateToAddCollegeFriends = useCallback(() => {
    router.push(`/profile/${userReader.username(user)}/add-college-friends`);
  }, [user]);

  const { fetchCollegeUsers, error } = useGetUsersOfMyInstitute({
    pageSize: 10,
  });

  const fetchCollegeUsersRef = useRef(fetchCollegeUsers);
  fetchCollegeUsersRef.current = fetchCollegeUsers;

  const [isLoading, setIsLoading] = useState(false);
  const [suggestedFriends, setSuggestedFriends] = useState([]);

  useEffect(() => {
    fetchCollegeUsersRef.current({ pageNumber: 1 }).then((res) => {
      setIsLoading(false);
      const { data } = res ?? EMPTY_OBJECT;
      const { getUsersOfMyInstitute: usersListObj } = data ?? EMPTY_OBJECT;
      const { results } = usersListObj ?? EMPTY_OBJECT;
      setSuggestedFriends(results);
    });
  }, []);

  if (!isAddCollegeFeatureAvailable || _isNil(currentUserInstitutionId)) {
    return null;
  }

  if (isLoading || !_isNil(error) || _isEmpty(suggestedFriends)) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={styles.header}>From your college</Text>
        <TouchableOpacity onPress={navigateToAddCollegeFriends}>
          <Text style={styles.viewAllText}>View All</Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ flexDirection: 'row', gap: 16 }}
        horizontal
      >
        <View />
        {_map(suggestedFriends, (friend) => (
          <FriendSuggestionCard key={friend?._id} userOutput={friend} />
        ))}
      </ScrollView>
    </View>
  );
};

export default React.memo(FriendsRecommendationList);
