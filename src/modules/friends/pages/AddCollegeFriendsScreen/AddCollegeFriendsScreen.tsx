import React, { useCallback, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Pressable,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Header from '@/src/components/shared/Header';
import Icon from 'react-native-vector-icons/Ionicons';
import dark from '@/src/core/constants/themes/dark';
import PaginatedList from '@/src/components/shared/PaginatedList';
import userReader from 'core/readers/userReader';
import WebBackButton from 'shared/WebBackButton';
import useMediaQuery from 'core/hooks/useMediaQuery';
import _isFunction from 'lodash/isFunction';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import useGetUsersOfMyInstitute from '../../hooks/queries/useGetUsersOfMyInstitute';
import useSendBulkFriendRequestsToInstitute from '../../hooks/mutations/useSendBulkFriendRequestsToInstitute';
import CollegeFriendListItem from '../../components/CollegeFriendListItem';
import styles from './AddCollegeFriendsScreen.style';

const PAGE_SIZE = 50;

interface AddCollegeFriendsScreenProps {
  isPopoverMode?: boolean;
  onCloseRequest?: () => void;
}

const AddCollegeFriendsScreen: React.FC<AddCollegeFriendsScreenProps> = ({
  isPopoverMode = false,
  onCloseRequest,
}) => {
  const { fetchCollegeUsers, error, updateCollegeUsersCache } =
    useGetUsersOfMyInstitute({
      pageSize: PAGE_SIZE,
    });

  const { sendBulkRequests, loading: mutationLoading } =
    useSendBulkFriendRequestsToInstitute();

  const paginatedListRef = useRef<any>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [sentCount, setSentCount] = useState(0);

  const { isMobile: isCompactDevice } = useMediaQuery();

  const handleSendBulkRequests = useCallback(async () => {
    if (mutationLoading || sentCount > 0) return;
    Analytics.track(ANALYTICS_EVENTS.PROFILE.CLICKED_ON_SEND_REQ_TO_ALL);
    try {
      const response = await sendBulkRequests();
      if (response?.data?.sendBulkFriendRequestsToInstitute) {
        const numSent = response?.data?.sendBulkFriendRequestsToInstitute;
        setSentCount(numSent);
        showToast({
          type: TOAST_TYPE.SUCCESS,
          description: `Sent to ${numSent} users`,
        });
        Analytics.track(ANALYTICS_EVENTS.PROFILE.SENT_FRIEND_REQUEST_TO_ALL, {
          numSent,
        });
        paginatedListRef.current?.loadData();
      } else if (response?.errors) {
        showToast({
          type: TOAST_TYPE.ERROR,
          description:
            response?.errors?.[0]?.message || 'Failed to send requests.',
        });
      }
    } catch (e: any) {
      showToast({
        type: TOAST_TYPE.ERROR,
        description: e?.message || 'An error occurred while sending requests.',
      });
    }
  }, [sendBulkRequests, mutationLoading, sentCount]);

  const fetchData = useCallback(
    async ({ pageNumber }: { pageNumber: number }) => {
      const response = await fetchCollegeUsers({ pageNumber });
      const { data } = response ?? EMPTY_OBJECT;
      const { getUsersOfMyInstitute: usersListObj } = data ?? EMPTY_OBJECT;

      const { results, totalResults } = usersListObj ?? EMPTY_OBJECT;

      setTotalCount(totalResults);
      return {
        data: results,
        totalItems: totalResults,
      };
    },
    [fetchCollegeUsers],
  );

  const renderCollegeFriendItem = useCallback(
    ({ item, index, onRemove }) => (
      <CollegeFriendListItem
        userOutput={item}
        key={`${userReader.id(item?.userPublicDetails)}-${index}`}
        onRemove={onRemove}
      />
    ),
    [],
  );

  if (error) {
    return (
      <View style={styles.container}>
        {!isPopoverMode && <Header title="Add College Friends" />}
        {!isCompactDevice && !isPopoverMode && (
          <View style={styles.headerExpanded}>
            <WebBackButton title="Add College Friends" />
          </View>
        )}
        {isPopoverMode && _isFunction(onCloseRequest) && (
          <View style={styles.popoverHeader}>
            <Text style={styles.popoverTitle}>Add College Friends</Text>
            <Pressable onPress={onCloseRequest} style={styles.closeButton}>
              <Icon name="close-outline" size={28} color={dark.colors.text} />
            </Pressable>
          </View>
        )}
        <View style={styles.centeredMessageContainer}>
          <Text style={styles.errorText}>
            Error fetching college mates: {error?.message}
          </Text>
        </View>
      </View>
    );
  }

  const renderSendReqAllButton = () => {
    if (mutationLoading) {
      return (
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <ActivityIndicator
            size="small"
            color={dark.colors.secondary}
            style={{ marginRight: 5 }}
          />
          <Text style={styles.sendReqAllText}>SENDING...</Text>
        </View>
      );
    }
    if (sentCount > 0) {
      return (
        <Text
          style={styles.sendReqAllText}
        >{`SENT TO ${sentCount} USERS`}</Text>
      );
    }

    if (totalCount === 0) return null;

    return (
      <TouchableOpacity
        onPress={handleSendBulkRequests}
        disabled={mutationLoading || sentCount > 0}
      >
        <Text style={styles.sendReqAllText}>SEND REQ TO ALL</Text>
      </TouchableOpacity>
    );
  };

  return (
    <View
      style={[
        styles.container,
        !isCompactDevice && !isPopoverMode && { paddingHorizontal: 20 },
        isPopoverMode && { paddingTop: 0 },
      ]}
    >
      {!isPopoverMode && <Header title="Add College Friends" />}
      {!isCompactDevice && !isPopoverMode && (
        <View style={styles.headerExpanded}>
          <WebBackButton title="Add College Friends" />
        </View>
      )}
      {isPopoverMode && _isFunction(onCloseRequest) && (
        <View style={styles.popoverHeader}>
          <Pressable onPress={onCloseRequest}>
            <Icon name="close-outline" size={25} color={dark.colors.text} />
          </Pressable>
          <Text style={styles.popoverTitle}>Add College Friends</Text>
        </View>
      )}
      <View
        style={[
          styles.noOfCollegeFriends,
          {
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          },
        ]}
      >
        <Text
          style={styles.noOfCollegeFriendsText}
        >{`${totalCount} COLLEGE USERS FOUND`}</Text>
        {renderSendReqAllButton()}
      </View>
      <PaginatedList
        ref={paginatedListRef}
        key="college-friends"
        fetchData={fetchData}
        renderItem={renderCollegeFriendItem}
        keyExtractor={(item) => userReader.id(item?.userPublicDetails)}
        pageSize={PAGE_SIZE}
        pullToRefreshEnabled={!isPopoverMode}
        emptyListComponent={
          <View style={styles.centeredMessageContainer}>
            <Text style={styles.emptyText}>
              No college mates found, or you haven't joined an institution yet.
            </Text>
          </View>
        }
        updateCacheFunction={updateCollegeUsersCache}
        dataKey="getUsersOfMyInstitute"
        hidePagination={isPopoverMode}
      />
    </View>
  );
};

export default React.memo(AddCollegeFriendsScreen);
