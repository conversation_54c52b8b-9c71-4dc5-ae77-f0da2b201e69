export const EVENTS = {
  PROFILE: {
    VIEWED: 'profile:view profile page',
    CLICKED_ON_PROFILE_PAGE_ALL_PREVIOUS_GAMES:
      'profile: click on profile page all previous games',
    CLICKED_ON_PROFILE_PAGE_KEBAB_MENU:
      'profile: click on profile page kebab menu',
    CLICKED_ON_PROFILE_PAGE_DELETE_ACCOUNT:
      'profile: click on profile page delete account',
    CLICKED_ON_SOCIAL_LINK: 'profile: clicked on social link',
    VIEWED_INSIGHTS_PAGE: 'profile: view insights page',
    CLICKED_ON_SHARE_REFERRAL_LINK:
      'referral: click on share code streak freezer',
    REFERRAL_CODE_REDEMPTION_SUCCESS: 'referral: used referral code',

    REFER_FRIEND_HOME_SCREEN_COMPONENT_SHARE_WA:
      'referral: clicked on invite via WhatsApp on home page',
    REFER_FRIEND_HOME_SCREEN_COMPONENT_SHARE_OTHER:
      'referral: clicked on invite via other methods on home page',
    INVITE_FRIENDS_BUTTON_AFTER_COLLEGE_CREATION:
      'add-college: clicked on invite friends button after college creation',
    CLICKED_ADD_COLLEGE: 'profile: clicked on add college',
    CLICKED_ADD_SOCIAL: 'profile: clicked on add social link',
    CLICKED_COLLEGE_LINK: 'profile: clicked on college link',
    CLICKED_ON_REMOVE_COLLEGE: 'profile: clicked on remove college',
    CLICKED_ON_CHANGE_COLLEGE: 'profile: clicked on change college',
    REMOVED_COLLEGE: 'profile : removed college',
    CLICKED_ON_EXIT_COLLEGE_ICON: 'profile: clicked on exit college icon',
    CLICKED_ON_ADD_NEW_COLLEGE: 'profile: clicked on add new college',
    CLICKED_ON_USER_PROFILE_FROM_COLLEGE_FRIENDS_TAB:
      'profile: clicked on user profile from college friends tab',
    CLICKED_ON_SEND_REQ_TO_ALL: 'profile: clicked on send req to all',
    SENT_FRIEND_REQUEST_TO_ALL: 'profile: sent friend request to all',
    VIEWED_ADD_COLLEGE_PAGE: 'profile: viewed add college page',
    VIEWED_ADD_COLLEGE_FRIENDS_PAGE: 'profile: viewed add college friends page',
  },
};
